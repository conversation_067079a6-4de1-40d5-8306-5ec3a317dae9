#ifndef BSP_USART__H
#define BSP_USART__H

#include "bsp_system.h"

extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_index;
extern uint8_t uart_rx_ticks;

extern struct rt_ringbuffer ringbuffer;
extern uint8_t uart_rx_dma_buffer[128];//接收缓存
extern uint8_t ringbuffer_pool[128];//环形缓冲区池

int fputc(int ch, FILE *stream);
int fputs(const char* restrict s, FILE* restrict stream);
int puts(const char* _ptr);

void uart0_send_char(char ch); //串口0发送单个字符

void uart0_send_string(char* str); //串口0发送字符串

void uart0_init(void);

void uart_proc(void);

#endif