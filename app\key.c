#include "key.h"
volatile int Flag_stop;
volatile int Flag_stop1;

// 按键控制结构体实例
key_ctrl_t key1_ctrl = {0};  // PA15按键控制结构体

// 系统时钟计数器（需要在定时器中断中递增）
static volatile uint32_t system_tick_ms = 0;
/**************************************************************************
Function: Key scan
Input   : Double click the waiting time
Output  : 0：No action；1：click；2：Double click
函数功能：按键扫描
入口参数：双击等待时间
返回  值：按键状态 0：无动作 1：单击 2：双击
**************************************************************************/
// uint8_t click_N_Double (uint8_t time)
// {
//     static  uint8_t flag_key,count_key,double_key=0;
//     static  uint16_t count_single,Forever_count;
//     if(DL_GPIO_readPins(KEY_PORT,KEY_key_PIN)>0)  Forever_count++;   //长按标志位未置1
//     else        Forever_count=0;
//     if((DL_GPIO_readPins(KEY_PORT,KEY_key_PIN)>0)&&0==flag_key)     flag_key=1; //第一次按下
//     if(0==count_key)
//     {
//             if(flag_key==1)
//             {
//                 double_key++;
//                 count_key=1;            //标记按下一次
//             }
//             if(double_key==3)
//             {                                       //按下两次
//                 double_key=0;
//                 count_single=0;
//                 return 2;                   //双击执行的指令
//             }
//     }
//     if(0==DL_GPIO_readPins(KEY_PORT,KEY_key_PIN))          flag_key=0,count_key=0;
//     if(1==double_key)
//     {
//         count_single++;
//         if(count_single>time&&Forever_count<time)
//         {
//             double_key=0;
//             count_single=0; //超时不标记为双击
// 			return 1;//单击执行的指令
//         }
//         if(Forever_count>time)
//         {
//             double_key=0;
//             count_single=0;
//         }
//     }
//     return 0;
// }
// /**************************************************************************
// Function: Long press detection
// Input   : none
// Output  : 0：No action；1：Long press for 2 seconds；
// 函数功能：长按检测
// 入口参数：无
// 返回  值：按键状态 0：无动作 1：长按2s
// **************************************************************************/
// uint8_t Long_Press(void)
// {
//         static uint16_t Long_Press_count,Long_Press;
//       if(Long_Press==0&&KEY==0)  Long_Press_count++;   //长按标志位未置1
//     else                       Long_Press_count=0;
//         if(Long_Press_count>200)        //长按标志位置1
//       {
//             Long_Press=1;
//             Long_Press_count=0;
//             return 1;
//         }
//         if(Long_Press==1)     //单击控制小车的启停
//         {
//             Long_Press=0;
//         }
//         return 0;
// }


// void Key(void)
// {
// 	u8 tmp,tmp2;
// 	tmp=click_N_Double(50);
// 	if(tmp==1)
// 	{
// 		Flag_Stop=!Flag_Stop;
//     }
// }
// void Key(void)
// {
// 	uint8_t tmp,tmp2;
// 	tmp=click_N_Double(50);
// 	if(tmp==1)
// 	{
// 		Flag_stop = 1;
//     }
//     else 
//     {
//         Flag_stop = 0;
//     }
// }

void Key(void)
{
    if(DL_GPIO_readPins(KEY_key_PORT, KEY_key_PIN))
    {
        while (DL_GPIO_readPins(KEY_key_PORT, KEY_key_PIN));
        Flag_stop = 1;
    }
    else 
    {
        Flag_stop = 0;
    
    }

}
/**
 * @brief 系统时钟递增函数（需要在10ms定时器中断中调用）
 */
void Key_System_Tick_Inc(void)
{
    system_tick_ms += 10;  // 每10ms递增一次
}

/**
 * @brief 按键防抖初始化
 */
void Key_Init_Debounce(void)
{
    key1_ctrl.state = KEY_STATE_IDLE;
    key1_ctrl.timer = 0;
    key1_ctrl.last_level = 0;
    key1_ctrl.press_flag = 0;
    key1_ctrl.long_press_flag = 0;
}

/**
 * @brief 按键防抖扫描算法
 * @param key_ctrl 按键控制结构体指针
 * @param current_level 当前按键电平状态 (1=按下, 0=释放)
 * @return key_event_t 按键事件
 */
key_event_t Key_Scan_Debounce(key_ctrl_t *key_ctrl, uint8_t current_level)
{
    key_event_t event = KEY_EVENT_NONE;

    switch(key_ctrl->state)
    {
        case KEY_STATE_IDLE:
            if(current_level && !key_ctrl->last_level)  // 检测到按下沿
            {
                key_ctrl->state = KEY_STATE_DEBOUNCE;
                key_ctrl->timer = system_tick_ms;
            }
            break;

        case KEY_STATE_DEBOUNCE:
            if(current_level)  // 持续按下
            {
                if((system_tick_ms - key_ctrl->timer) >= KEY_DEBOUNCE_TIME)
                {
                    key_ctrl->state = KEY_STATE_PRESSED;
                    key_ctrl->timer = system_tick_ms;
                    key_ctrl->press_flag = 1;
                    event = KEY_EVENT_PRESS;  // 产生短按事件
                }
            }
            else  // 在防抖期间释放，认为是抖动
            {
                key_ctrl->state = KEY_STATE_IDLE;
            }
            break;

        case KEY_STATE_PRESSED:
            if(current_level)  // 持续按下
            {
                if((system_tick_ms - key_ctrl->timer) >= KEY_LONG_PRESS_TIME)
                {
                    key_ctrl->state = KEY_STATE_LONG_PRESS;
                    key_ctrl->long_press_flag = 1;
                    event = KEY_EVENT_LONG_PRESS;  // 产生长按事件
                }
            }
            else  // 释放
            {
                key_ctrl->state = KEY_STATE_RELEASE;
                key_ctrl->timer = system_tick_ms;
            }
            break;

        case KEY_STATE_LONG_PRESS:
            if(!current_level)  // 长按后释放
            {
                key_ctrl->state = KEY_STATE_RELEASE;
                key_ctrl->timer = system_tick_ms;
            }
            break;

        case KEY_STATE_RELEASE:
            if(!current_level)  // 持续释放
            {
                if((system_tick_ms - key_ctrl->timer) >= KEY_DEBOUNCE_TIME)
                {
                    key_ctrl->state = KEY_STATE_IDLE;
                    key_ctrl->press_flag = 0;
                    key_ctrl->long_press_flag = 0;
                    event = KEY_EVENT_RELEASE;  // 产生释放事件
                }
            }
            else  // 在释放防抖期间又按下
            {
                key_ctrl->state = KEY_STATE_DEBOUNCE;
                key_ctrl->timer = system_tick_ms;
            }
            break;

        default:
            key_ctrl->state = KEY_STATE_IDLE;
            break;
    }

    key_ctrl->last_level = current_level;
    return event;
}

/**
 * @brief PA15按键扫描（带防抖）
 */
void Key_1(void)
{
    uint8_t key_level = DL_GPIO_readPins(KEY_PIN_3_PORT, KEY_PIN_3_PIN) ? 1 : 0;
    key_event_t event = Key_Scan_Debounce(&key1_ctrl, key_level);

    // 处理按键事件
    switch(event)
    {
        case KEY_EVENT_PRESS:
            Flag_stop1 = 1;  // 短按事件
            break;

        case KEY_EVENT_LONG_PRESS:
            // 可以添加长按功能，比如复位或特殊功能
            // Flag_stop1 = 2;  // 长按事件（如果需要）
            break;

        case KEY_EVENT_RELEASE:
            // 按键释放事件（如果需要处理）
            break;

        case KEY_EVENT_NONE:
        default:
            // 无事件，保持当前状态
            break;
    }
}
