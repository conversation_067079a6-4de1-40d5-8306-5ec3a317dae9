/**
 * @file Track_Example.c
 * @brief 循迹系统使用示例
 * @details 展示如何在不同场景下使用优化后的循迹系统
 */

#include "Ganway_Optimized.h"
#include "Track_Config.h"
#include "bsp_system.h"

// 示例：基础使用方法
void Example_Basic_Usage(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    
    // 1. 初始化循迹系统
    Track_Init();
    
    // 2. 设置为加权模式（推荐）
    Track_Set_Mode(TRACK_MODE_WEIGHTED);
    
    // 3. 设置合适的速度
    Track_Set_Speed(TRACK_BASE_SPEED_NORMAL);
    
    // 4. 主循环
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        // 执行循迹
        Way_Optimized(digital_data, analog_data);
        
        // 可选：输出调试信息
        Track_Debug_Info();
        
        delay_ms(10);  // 控制循环频率
    }
}

// 示例：按键切换模式
void Example_Mode_Switch(No_MCU_Sensor *sensor)
{
    static Track_Mode_t current_mode = TRACK_MODE_WEIGHTED;
    unsigned char digital_data;
    unsigned short analog_data[8];
    
    // 初始化
    Track_Init();
    Track_Set_Mode(current_mode);
    
    while(1) {
        // 检查按键（假设有按键切换功能）
        if(/* 按键按下条件 */ 0) {
            // 循环切换模式
            current_mode = (current_mode + 1) % 4;
            Track_Set_Mode(current_mode);
            
            // 显示当前模式（可选）
            switch(current_mode) {
                case TRACK_MODE_BASIC:
                    // OLED_ShowString(0, 0, "Mode: Basic", 12, 1);
                    break;
                case TRACK_MODE_WEIGHTED:
                    // OLED_ShowString(0, 0, "Mode: Weight", 12, 1);
                    break;
                case TRACK_MODE_PID:
                    // OLED_ShowString(0, 0, "Mode: PID", 12, 1);
                    break;
                case TRACK_MODE_ADAPTIVE:
                    // OLED_ShowString(0, 0, "Mode: Adapt", 12, 1);
                    break;
            }
            delay_ms(200);  // 防抖
        }
        
        // 获取传感器数据并执行循迹
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        Way_Optimized(digital_data, analog_data);
        
        delay_ms(10);
    }
}

// 示例：自适应速度控制
void Example_Adaptive_Speed(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        // 执行循迹
        Way_Optimized(digital_data, analog_data);
        
        // 获取状态信息
        status = Track_Get_Status();
        
        // 根据循迹状态自适应调整速度
        switch(status->state) {
            case TRACK_STATE_NORMAL:
                // 正常状态，使用标准速度
                Track_Set_Speed(TRACK_BASE_SPEED_NORMAL);
                break;
                
            case TRACK_STATE_TURN_LEFT:
            case TRACK_STATE_TURN_RIGHT:
                // 转弯状态，降低速度
                Track_Set_Speed(TRACK_BASE_SPEED_NORMAL * 0.8f);
                break;
                
            case TRACK_STATE_INTERSECTION:
                // 路口状态，进一步降低速度
                Track_Set_Speed(TRACK_BASE_SPEED_NORMAL * 0.6f);
                break;
                
            case TRACK_STATE_LOST:
                // 丢线状态，大幅降低速度
                Track_Set_Speed(TRACK_BASE_SPEED_NORMAL * 0.4f);
                break;
                
            default:
                break;
        }
        
        delay_ms(10);
    }
}

// 示例：PID参数在线调整
void Example_PID_Tuning(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    float kp = TRACK_PID_KP, ki = TRACK_PID_KI, kd = TRACK_PID_KD;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_PID);
    
    while(1) {
        // 获取传感器数据并执行循迹
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        Way_Optimized(digital_data, analog_data);
        
        // 模拟按键调整PID参数（实际应用中可以通过按键或串口调整）
        if(/* 增大Kp按键 */ 0) {
            kp += 0.1f;
            Track_Set_PID_Params(kp, ki, kd);
        }
        if(/* 减小Kp按键 */ 0) {
            kp -= 0.1f;
            if(kp < 0) kp = 0;
            Track_Set_PID_Params(kp, ki, kd);
        }
        if(/* 增大Ki按键 */ 0) {
            ki += 0.01f;
            Track_Set_PID_Params(kp, ki, kd);
        }
        if(/* 减小Ki按键 */ 0) {
            ki -= 0.01f;
            if(ki < 0) ki = 0;
            Track_Set_PID_Params(kp, ki, kd);
        }
        if(/* 增大Kd按键 */ 0) {
            kd += 0.01f;
            Track_Set_PID_Params(kp, ki, kd);
        }
        if(/* 减小Kd按键 */ 0) {
            kd -= 0.01f;
            if(kd < 0) kd = 0;
            Track_Set_PID_Params(kp, ki, kd);
        }
        if(/* 重置积分按键 */ 0) {
            Track_Reset_PID_Integral();
        }
        
        // 显示当前PID参数（可选）
        // OLED_ShowString(0, 0, "Kp:", 12, 1);
        // OLED_ShowSignedNum(18, 0, (int)(kp * 100), 3, 12, 1);
        // OLED_ShowString(0, 12, "Ki:", 12, 1);
        // OLED_ShowSignedNum(18, 12, (int)(ki * 1000), 3, 12, 1);
        // OLED_ShowString(0, 24, "Kd:", 12, 1);
        // OLED_ShowSignedNum(18, 24, (int)(kd * 100), 3, 12, 1);
        
        delay_ms(10);
    }
}

// 示例：紧急停止和安全保护
void Example_Safety_Protection(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    int lost_time_count = 0;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_WEIGHTED);
    
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        // 检查紧急停止条件
        if(/* 紧急停止按键 */ 0) {
            Track_Emergency_Stop();
            break;  // 退出循环
        }
        
        // 执行循迹
        Way_Optimized(digital_data, analog_data);
        
        // 获取状态并进行安全检查
        status = Track_Get_Status();
        
        // 丢线时间过长的保护
        if(status->state == TRACK_STATE_LOST) {
            lost_time_count++;
            if(lost_time_count > TRACK_LOST_LINE_TIMEOUT / 10) {  // 假设10ms循环
                // 丢线时间过长，紧急停止
                Track_Emergency_Stop();
                // OLED_ShowString(0, 0, "LOST LINE!", 12, 1);
                break;
            }
        } else {
            lost_time_count = 0;  // 重置计数
        }
        
        // 其他安全检查...
        
        delay_ms(10);
    }
}

// 示例：兼容性测试（使用原有算法）
void Example_Compatibility_Test(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        
        // 使用原有算法（已优化参数）
        Way(digital_data);
        
        delay_ms(10);
    }
}
