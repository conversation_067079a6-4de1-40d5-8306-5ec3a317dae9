/**
 * @file Square_Track_Test.c
 * @brief 正方形轨道精确转弯控制测试程序
 * @details 用于测试和验证"一轮停止，一轮转动"的直角转弯策略
 */

#include "Square_Track_Example.h"
#include "Ganway_Optimized.h"
#include "bsp_system.h"

/**
 * @brief 测试精确直角转弯控制
 * @param sensor 传感器对象指针
 * @note 这是一个简化的测试函数，专门用于验证直角转弯逻辑
 */
void Test_Precise_Corner_Control(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    int test_counter = 0;
    
    // 初始化系统
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
    
    // 显示测试开始信息
    #if TRACK_DEBUG_ENABLE
    // OLED_ShowString(0, 0, "Corner Test", 12, 1);
    // OLED_ShowString(0, 12, "Starting...", 12, 1);
    // OLED_Refresh();
    #endif
    
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        // 获取当前状态
        status = Track_Get_Status();
        
        // 执行循迹控制
        Way_Optimized(digital_data, analog_data);
        
        // 每100次循环输出一次状态信息
        test_counter++;
        if(test_counter >= 100) {
            test_counter = 0;
            
            #if TRACK_DEBUG_ENABLE
            // 显示当前状态
            // OLED_ShowString(0, 0, "State:", 12, 1);
            // OLED_ShowNum(36, 0, status->state, 1, 12, 1);
            // OLED_ShowString(0, 12, "Speed:", 12, 1);
            // OLED_ShowNum(36, 12, status->base_speed, 4, 12, 1);
            // OLED_ShowString(0, 24, "Error:", 12, 1);
            // OLED_ShowSignedNum(36, 24, status->error, 3, 12, 1);
            
            // 显示转弯状态
            if(status->state == TRACK_STATE_TURN_LEFT) {
                // OLED_ShowString(0, 36, "LEFT TURN", 12, 1);
            } else if(status->state == TRACK_STATE_TURN_RIGHT) {
                // OLED_ShowString(0, 36, "RIGHT TURN", 12, 1);
            } else if(status->state == TRACK_STATE_NORMAL) {
                // OLED_ShowString(0, 36, "STRAIGHT", 12, 1);
            } else if(status->state == TRACK_STATE_LOST) {
                // OLED_ShowString(0, 36, "LOST LINE", 12, 1);
            }
            
            // OLED_Refresh();
            #endif
        }
        
        delay_ms(10);  // 控制循环频率
    }
}

/**
 * @brief 手动测试直角转弯控制
 * @note 通过按键手动触发不同的转弯测试
 */
void Manual_Corner_Test(void)
{
    int test_mode = 0;
    
    #if TRACK_DEBUG_ENABLE
    // OLED_ShowString(0, 0, "Manual Test", 12, 1);
    // OLED_ShowString(0, 12, "Press Key", 12, 1);
    // OLED_Refresh();
    #endif
    
    while(1) {
        // 检查按键输入（假设有按键检测函数）
        // if(Key_Scan() == KEY_PRESSED) {
        //     test_mode = (test_mode + 1) % 4;
        // }
        
        switch(test_mode) {
            case 0:
                // 测试直行
                Set_PWM(TRACK_SQUARE_STRAIGHT_SPEED, TRACK_SQUARE_STRAIGHT_SPEED);
                // OLED_ShowString(0, 24, "STRAIGHT", 12, 1);
                break;
                
            case 1:
                // 测试左转（右轮转，左轮停）
                Set_PWM(0, TRACK_SQUARE_CORNER_SPEED * TRACK_SQUARE_CORNER_RATIO);
                // OLED_ShowString(0, 24, "LEFT TURN", 12, 1);
                break;
                
            case 2:
                // 测试右转（左轮转，右轮停）
                Set_PWM(TRACK_SQUARE_CORNER_SPEED * TRACK_SQUARE_CORNER_RATIO, 0);
                // OLED_ShowString(0, 24, "RIGHT TURN", 12, 1);
                break;
                
            case 3:
                // 停止
                Set_PWM(0, 0);
                // OLED_ShowString(0, 24, "STOP", 12, 1);
                break;
        }
        
        // OLED_ShowString(0, 36, "Mode:", 12, 1);
        // OLED_ShowNum(30, 36, test_mode, 1, 12, 1);
        // OLED_Refresh();
        
        delay_ms(100);
    }
}

/**
 * @brief 速度对比测试
 * @param sensor 传感器对象指针
 * @note 对比传统控制和精确控制的效果
 */
void Speed_Comparison_Test(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    static int test_phase = 0;
    static int phase_counter = 0;
    
    Track_Init();
    
    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        phase_counter++;
        
        // 每10秒切换一次测试模式
        if(phase_counter >= 1000) {  // 10秒 = 1000 * 10ms
            phase_counter = 0;
            test_phase = (test_phase + 1) % 3;
            
            switch(test_phase) {
                case 0:
                    // 使用基础模式（传统控制）
                    Track_Set_Mode(TRACK_MODE_BASIC);
                    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
                    // OLED_ShowString(0, 0, "Basic Mode", 12, 1);
                    break;
                    
                case 1:
                    // 使用加权模式
                    Track_Set_Mode(TRACK_MODE_WEIGHTED);
                    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
                    // OLED_ShowString(0, 0, "Weighted Mode", 12, 1);
                    break;
                    
                case 2:
                    // 使用自适应模式（包含精确转弯控制）
                    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
                    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
                    // OLED_ShowString(0, 0, "Adaptive Mode", 12, 1);
                    break;
            }
            
            // OLED_Refresh();
        }
        
        Way_Optimized(digital_data, analog_data);
        delay_ms(10);
    }
}

/**
 * @brief 参数调优测试
 * @param sensor 传感器对象指针
 * @note 用于实时调整和测试不同的参数组合
 */
void Parameter_Tuning_Test(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    static int tuning_counter = 0;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    
    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        status = Track_Get_Status();
        
        // 根据当前状态动态调整参数
        if(status->state == TRACK_STATE_TURN_LEFT || status->state == TRACK_STATE_TURN_RIGHT) {
            // 在转弯时可以实时调整转弯参数
            // 这里可以添加按键检测来调整参数
        }
        
        Way_Optimized(digital_data, analog_data);
        
        // 定期输出调试信息
        tuning_counter++;
        if(tuning_counter >= 50) {
            tuning_counter = 0;
            
            #if TRACK_DEBUG_ENABLE
            Track_Debug_Info();
            #endif
        }
        
        delay_ms(10);
    }
}
