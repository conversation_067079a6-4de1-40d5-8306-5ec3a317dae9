/**
 * @file Square_Track_Example.h
 * @brief 正方形轨道循迹专用示例函数声明
 * @details 包含各种正方形轨道循迹的示例和测试函数
 */

#ifndef _SQUARE_TRACK_EXAMPLE_H
#define _SQUARE_TRACK_EXAMPLE_H

#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"

/**
 * @brief 正方形轨道循迹示例 - 基础配置
 * @param sensor 传感器对象指针
 * @note 使用自适应模式和优化的速度配置
 */
void Square_Track_Basic_Example(No_MCU_Sensor *sensor);

/**
 * @brief 正方形轨道循迹示例 - 高级配置（动态速度调整）
 * @param sensor 传感器对象指针
 * @note 根据循迹状态动态调整速度，提高转弯精度
 */
void Square_Track_Advanced_Example(No_MCU_Sensor *sensor);

/**
 * @brief 正方形轨道循迹示例 - 调试模式
 * @param sensor 传感器对象指针
 * @note 包含详细的调试信息输出，便于参数调优
 */
void Square_Track_Debug_Example(No_MCU_Sensor *sensor);

/**
 * @brief 正方形轨道参数测试示例
 * @param sensor 传感器对象指针
 * @note 用于测试不同参数组合的效果
 */
void Square_Track_Parameter_Test(No_MCU_Sensor *sensor);

/**
 * @brief 正方形轨道精确直角转弯示例
 * @param sensor 传感器对象指针
 * @note 专门演示"一轮停止，一轮转动"的直角转弯策略
 *       解决速度过快导致的拐角过不去问题
 */
void Square_Track_Precise_Corner_Example(No_MCU_Sensor *sensor);

/**
 * @brief 正方形轨道速度自适应示例
 * @param sensor 传感器对象指针
 * @note 根据轨道状态动态调整速度，直角转弯时自动降速并使用精确控制
 */
void Square_Track_Speed_Adaptive_Example(No_MCU_Sensor *sensor);

#endif /* _SQUARE_TRACK_EXAMPLE_H */
