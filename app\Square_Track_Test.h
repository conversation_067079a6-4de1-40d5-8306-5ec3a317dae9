/**
 * @file Square_Track_Test.h
 * @brief 正方形轨道精确转弯控制测试程序头文件
 * @details 包含各种测试函数的声明
 */

#ifndef _SQUARE_TRACK_TEST_H
#define _SQUARE_TRACK_TEST_H

#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"

/**
 * @brief 测试精确直角转弯控制
 * @param sensor 传感器对象指针
 * @note 这是一个简化的测试函数，专门用于验证直角转弯逻辑
 */
void Test_Precise_Corner_Control(No_MCU_Sensor *sensor);

/**
 * @brief 手动测试直角转弯控制
 * @note 通过按键手动触发不同的转弯测试
 */
void Manual_Corner_Test(void);

/**
 * @brief 速度对比测试
 * @param sensor 传感器对象指针
 * @note 对比传统控制和精确控制的效果
 */
void Speed_Comparison_Test(No_MCU_Sensor *sensor);

/**
 * @brief 参数调优测试
 * @param sensor 传感器对象指针
 * @note 用于实时调整和测试不同的参数组合
 */
void Parameter_Tuning_Test(No_MCU_Sensor *sensor);

#endif /* _SQUARE_TRACK_TEST_H */
