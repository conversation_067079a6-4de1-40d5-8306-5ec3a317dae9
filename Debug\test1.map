******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 19:24:36 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000344d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000057b8  0001a848  R  X
  SRAM                  20200000   00008000  000009b7  00007649  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000057b8   000057b8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003c08   00003c08    r-x .text
  00003cc8    00003cc8    00001a80   00001a80    r-- .rodata
  00005748    00005748    00000070   00000070    r-- .cinit
20200000    20200000    000007ba   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    0000025a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003c08     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    000001b4     motor.o (.text.Motor_Square_Corner_Control)
                  00000444    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000005d8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000076a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000076c    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000008f4    0000016c     motor.o (.text.Set_PWM)
                  00000a60    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000bbc    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000d10    0000014c     empty.o (.text.main)
                  00000e5c    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f7c    00000110     motor.o (.text.Motor_Smooth_Control)
                  0000108c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001198    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000129c    00000100     empty.o (.text.TIMG0_IRQHandler)
                  0000139c    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001484    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001568    000000e2     oled.o (.text.OLED_ShowNum)
                  0000164a    000000de     oled.o (.text.OLED_Init)
                  00001728    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001804    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000018dc    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000019ac    000000b8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  00001a64    000000b8     motor.o (.text.Motor_PID_Control)
                  00001b1c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001bc6    00000002     --HOLE-- [fill = 0]
                  00001bc8    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001c70    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001d18    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001db2    0000009a     oled.o (.text.OLED_ShowString)
                  00001e4c    00000090     oled.o (.text.OLED_DrawPoint)
                  00001edc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001f68    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001ff4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002080    00000084     oled.o (.text.OLED_Refresh)
                  00002104    00000084     Ganway_Optimized.o (.text.Way_Optimized)
                  00002188    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000220c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000228e    00000002     --HOLE-- [fill = 0]
                  00002290    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000230c    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  00002380    00000074     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  000023f4    00000074     Ganway_Optimized.o (.text.Track_Init)
                  00002468    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000024dc    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  0000254e    00000002     --HOLE-- [fill = 0]
                  00002550    0000006c     oled.o (.text.OLED_WR_Byte)
                  000025bc    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002628    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  00002690    00000068     key.o (.text.Key_1)
                  000026f8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002760    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000027c2    00000002     --HOLE-- [fill = 0]
                  000027c4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002826    00000002     --HOLE-- [fill = 0]
                  00002828    00000060     oled.o (.text.OLED_Clear)
                  00002888    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000028e6    00000002     --HOLE-- [fill = 0]
                  000028e8    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002940    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002994    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000029e4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002a34    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002a80    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002acc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002b16    00000002     --HOLE-- [fill = 0]
                  00002b18    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002b62    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002bac    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002bf4    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002c3c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002c84    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002ccc    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002d10    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002d52    00000002     --HOLE-- [fill = 0]
                  00002d54    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002d94    00000040     key.o (.text.Key)
                  00002dd4    00000040     Ganway_Optimized.o (.text.Track_Square_Corner_Control)
                  00002e14    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002e54    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002e94    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002ed0    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002f0c    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002f48    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002f84    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002fc0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002ffc    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003038    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003074    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000030ae    00000002     --HOLE-- [fill = 0]
                  000030b0    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000030ea    00000002     --HOLE-- [fill = 0]
                  000030ec    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003124    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003158    00000034     oled.o (.text.OLED_ColorTurn)
                  0000318c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000031c0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000031f4    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00003224    00000030     oled.o (.text.OLED_Pow)
                  00003254    00000030     systick.o (.text.SysTick_Handler)
                  00003284    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000032b0    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000032dc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003308    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  00003334    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  0000335c    00000028     empty.o (.text.DL_Common_updateReg)
                  00003384    00000028     oled.o (.text.DL_Common_updateReg)
                  000033ac    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000033d4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000033fc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003424    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0000344c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003474    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000349a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000034c0    00000024     motor.o (.text.Left_Control)
                  000034e4    00000024     motor.o (.text.Left_Little_Control)
                  00003508    00000024     motor.o (.text.Right_Control)
                  0000352c    00000024     motor.o (.text.Right_Little_Control)
                  00003550    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003574    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003594    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000035b4    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  000035d4    00000020     systick.o (.text.delay_ms)
                  000035f4    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003612    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003630    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  0000364c    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00003668    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00003684    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000036a0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000036bc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000036d8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000036f4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003710    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000372c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003748    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003764    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003780    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000379c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000037b4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000037cc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000037e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000037fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003814    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000382c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003844    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000385c    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00003874    00000018     empty.o (.text.DL_GPIO_setPins)
                  0000388c    00000018     motor.o (.text.DL_GPIO_setPins)
                  000038a4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000038bc    00000018     empty.o (.text.DL_GPIO_togglePins)
                  000038d4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000038ec    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003904    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000391c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003934    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000394c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003964    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000397c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003994    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000039ac    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000039c4    00000018     empty.o (.text.DL_Timer_startCounter)
                  000039dc    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000039f4    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003a0c    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  00003a24    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00003a3a    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00003a50    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00003a66    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003a7c    00000016     key.o (.text.DL_GPIO_readPins)
                  00003a92    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003aa8    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003abc    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003ad0    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003ae4    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003af8    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003b0c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003b20    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003b34    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003b48    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003b5c    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003b70    00000014     key.o (.text.Key_Init_Debounce)
                  00003b84    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003b96    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003ba8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003bba    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003bcc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003bde    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003bee    00000002     --HOLE-- [fill = 0]
                  00003bf0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003c00    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003c10    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003c20    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003c30    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003c3e    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003c4c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003c5a    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003c66    00000002     --HOLE-- [fill = 0]
                  00003c68    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003c74    0000000c     systick.o (.text.get_systicks)
                  00003c80    0000000c     Scheduler.o (.text.scheduler_init)
                  00003c8c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003c96    00000002     --HOLE-- [fill = 0]
                  00003c98    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003ca0    00000006     libc.a : exit.c.obj (.text:abort)
                  00003ca6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003caa    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003cae    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003cb2    00000002     --HOLE-- [fill = 0]
                  00003cb4    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003cc4    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005748    00000070     
                  00005748    0000004a     (.cinit..data.load) [load image, compression = lzss]
                  00005792    00000002     --HOLE-- [fill = 0]
                  00005794    0000000c     (__TI_handler_table)
                  000057a0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000057a8    00000010     (__TI_cinit_table)

.rodata    0    00003cc8    00001a80     
                  00003cc8    00000d5c     oled.o (.rodata.asc2_2412)
                  00004a24    000005f0     oled.o (.rodata.asc2_1608)
                  00005014    00000474     oled.o (.rodata.asc2_1206)
                  00005488    00000228     oled.o (.rodata.asc2_0806)
                  000056b0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000056d8    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  000056f8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000570c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00005716    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005718    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00005720    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00005728    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  00005730    00000006     motor.o (.rodata.str1.10718775090649846465.1)
                  00005736    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00005739    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000573c    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  0000573f    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00005741    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    0000025a     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000024     motor.o (.data.motor_pid)
                  2020073c    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  2020075c    00000010     empty.o (.data.Anolog)
                  2020076c    00000010     empty.o (.data.black)
                  2020077c    00000010     empty.o (.data.white)
                  2020078c    0000000c     key.o (.data.key1_ctrl)
                  20200798    00000008     systick.o (.data.systicks)
                  202007a0    00000004     empty.o (.data.D_Num)
                  202007a4    00000004     motor.o (.data.Motor_Square_Corner_Control.last_turn_direction)
                  202007a8    00000004     motor.o (.data.Motor_Square_Corner_Control.sharp_turn_counter)
                  202007ac    00000004     empty.o (.data.Run)
                  202007b0    00000004     systick.o (.data.delay_times)
                  202007b4    00000004     key.o (.data.system_tick_ms)
                  202007b8    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b9    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       motor.o                          1760    14        100    
       Ganway_Optimized.o               1408    32        32     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5530    46        165    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       110       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     15340   7079      2487   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000057a8 records: 2, size/record: 8, table size: 16
	.data: load addr=00005748, load size=0000004a bytes, run addr=20200560, run size=0000025a bytes, compression=lzss
	.bss: load addr=000057a0, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005794 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   0000344d     00003cb4     00003cae   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003ca7  ADC0_IRQHandler                      
00003ca7  ADC1_IRQHandler                      
00003ca7  AES_IRQHandler                       
000019ad  Analyze_Track_State                  
2020075c  Anolog                               
00003caa  C$$EXIT                              
00003ca7  CANFD0_IRQHandler                    
00001bc9  Calculate_Line_Position              
00003335  Calculate_Position_Error             
00003ca7  DAC0_IRQHandler                      
00002d55  DL_ADC12_setClockConfig              
00003c8d  DL_Common_delayCycles                
00002889  DL_I2C_fillControllerTXFIFO          
0000349b  DL_I2C_setClockConfig                
00001729  DL_SYSCTL_configSYSPLL               
00002ccd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001199  DL_Timer_initFourCCPWMMode           
0000139d  DL_Timer_initTimerMode               
00003749  DL_Timer_setCaptCompUpdateMethod     
000039ad  DL_Timer_setCaptureCompareOutCtl     
00003c01  DL_Timer_setCaptureCompareValue      
00003765  DL_Timer_setClockConfig              
00002bad  DL_UART_init                         
00003ba9  DL_UART_setClockConfig               
00003ca7  DMA_IRQHandler                       
202007a0  D_Num                                
00003ca7  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003ca7  GROUP0_IRQHandler                    
00000e5d  GROUP1_IRQHandler                    
000018dd  Get_Analog_value                     
00002f0d  Get_Anolog_Value                     
00003c31  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003cab  HOSTexit                             
00003a0d  Handle_Intersection                  
00002629  Handle_Lost_Line                     
00003ca7  HardFault_Handler                    
00003ca7  I2C0_IRQHandler                      
00003ca7  I2C1_IRQHandler                      
00002d95  Key                                  
00002691  Key_1                                
00003b71  Key_Init_Debounce                    
00000a61  Key_Scan_Debounce                    
00003c11  Key_System_Tick_Inc                  
000034c1  Left_Control                         
000034e5  Left_Little_Control                  
00001a65  Motor_PID_Control                    
000035b5  Motor_Reset_Speed_Monitor            
00000f7d  Motor_Smooth_Control                 
00001c71  Motor_Speed_Monitor                  
00000291  Motor_Square_Corner_Control          
0000230d  Motor_Verify_Speed_Consistency       
00003ca7  NMI_Handler                          
0000076d  No_MCU_Ganv_Sensor_Init              
000024dd  No_MCU_Ganv_Sensor_Init_Frist        
00002d11  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002829  OLED_Clear                           
00003159  OLED_ColorTurn                       
00002bf5  OLED_DisplayTurn                     
00001e4d  OLED_DrawPoint                       
20200000  OLED_GRAM                            
0000164b  OLED_Init                            
00003225  OLED_Pow                             
00002081  OLED_Refresh                         
000000c1  OLED_ShowChar                        
00001569  OLED_ShowNum                         
00001d19  OLED_ShowSignedNum                   
00001db3  OLED_ShowString                      
00002551  OLED_WR_Byte                         
00003ca7  PendSV_Handler                       
00003ca7  RTC_IRQHandler                       
00003caf  Reset_Handler                        
00003509  Right_Control                        
0000352d  Right_Little_Control                 
202007ac  Run                                  
00003ca7  SPI0_IRQHandler                      
00003ca7  SPI1_IRQHandler                      
00003ca7  SVC_Handler                          
00002c3d  SYSCFG_DL_ADC12_0_init               
00000445  SYSCFG_DL_GPIO_init                  
000028e9  SYSCFG_DL_I2C_OLED_init              
00001edd  SYSCFG_DL_PWM_0_init                 
00002c85  SYSCFG_DL_SYSCTL_init                
00003c5b  SYSCFG_DL_SYSTICK_init               
0000318d  SYSCFG_DL_TIMER_0_init               
00002941  SYSCFG_DL_UART_0_init                
000031c1  SYSCFG_DL_init                       
00001f69  SYSCFG_DL_initPower                  
000008f5  Set_PWM                              
00003255  SysTick_Handler                      
00003ca7  TIMA0_IRQHandler                     
00003ca7  TIMA1_IRQHandler                     
0000129d  TIMG0_IRQHandler                     
00003ca7  TIMG12_IRQHandler                    
00003ca7  TIMG6_IRQHandler                     
00003ca7  TIMG7_IRQHandler                     
00003ca7  TIMG8_IRQHandler                     
00003bbb  TI_memcpy_small                      
00003c4d  TI_memset_small                      
00002381  Track_Adaptive_Control               
00000bbd  Track_Basic_Control                  
000023f5  Track_Init                           
00002f49  Track_PID_Control                    
00002dd5  Track_Square_Corner_Control          
00002f85  Track_Weighted_Control               
00002e15  UART0_IRQHandler                     
00003ca7  UART1_IRQHandler                     
00003ca7  UART2_IRQHandler                     
00003ca7  UART3_IRQHandler                     
00002105  Way_Optimized                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000057a8  __TI_CINIT_Base                      
000057b8  __TI_CINIT_Limit                     
000057b8  __TI_CINIT_Warm                      
00005794  __TI_Handler_Table_Base              
000057a0  __TI_Handler_Table_Limit             
00003039  __TI_auto_init_nobinit_nopinit       
00002291  __TI_decompress_lzss                 
00003bcd  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003c21  __TI_zero_init                       
000005e3  __adddf3                             
0000180f  __addsf3                             
00002b19  __aeabi_d2iz                         
000005e3  __aeabi_dadd                         
00002761  __aeabi_dcmpeq                       
0000279d  __aeabi_dcmpge                       
000027b1  __aeabi_dcmpgt                       
00002789  __aeabi_dcmple                       
00002775  __aeabi_dcmplt                       
0000108d  __aeabi_ddiv                         
00001485  __aeabi_dmul                         
000005d9  __aeabi_dsub                         
000030ed  __aeabi_f2iz                         
0000180f  __aeabi_fadd                         
000027c5  __aeabi_fcmpeq                       
00002801  __aeabi_fcmpge                       
00002815  __aeabi_fcmpgt                       
000027ed  __aeabi_fcmple                       
000027d9  __aeabi_fcmplt                       
0000220d  __aeabi_fdiv                         
00001ff5  __aeabi_fmul                         
00001805  __aeabi_fsub                         
000032dd  __aeabi_i2d                          
00002fc1  __aeabi_i2f                          
0000076b  __aeabi_idiv0                        
00003c69  __aeabi_memclr                       
00003c69  __aeabi_memclr4                      
00003c69  __aeabi_memclr8                      
00003c99  __aeabi_memcpy                       
00003c99  __aeabi_memcpy4                      
00003c99  __aeabi_memcpy8                      
00003551  __aeabi_ui2d                         
00003425  __aeabi_ui2f                         
00002e55  __aeabi_uidiv                        
00002e55  __aeabi_uidivmod                     
ffffffff  __binit__                            
000026f9  __cmpdf2                             
00003075  __cmpsf2                             
0000108d  __divdf3                             
0000220d  __divsf3                             
000026f9  __eqdf2                              
00003075  __eqsf2                              
00002b19  __fixdfsi                            
000030ed  __fixsfsi                            
000032dd  __floatsidf                          
00002fc1  __floatsisf                          
00003551  __floatunsidf                        
00003425  __floatunsisf                        
00002469  __gedf2                              
00002ffd  __gesf2                              
00002469  __gtdf2                              
00002ffd  __gtsf2                              
000026f9  __ledf2                              
00003075  __lesf2                              
000026f9  __ltdf2                              
00003075  __ltsf2                              
UNDEFED   __mpu_init                           
00001485  __muldf3                             
000030b1  __muldsi3                            
00001ff5  __mulsf3                             
000026f9  __nedf2                              
00003075  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000005d9  __subdf3                             
00001805  __subsf3                             
0000344d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003cc5  _system_pre_init                     
00003ca1  abort                                
00002b63  adc_getValue                         
00005488  asc2_0806                            
00005014  asc2_1206                            
00004a24  asc2_1608                            
00003cc8  asc2_2412                            
ffffffff  binit                                
2020076c  black                                
000025bd  convertAnalogToDigital               
000035d5  delay_ms                             
202007b0  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003c75  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
2020078c  key1_ctrl                            
00000d11  main                                 
20200718  motor_pid                            
00001b1d  normalizeAnalogValues                
20200560  rx_buff                              
00003c81  scheduler_init                       
00003c3f  strcpy                               
00003309  strncpy                              
2020055c  task_num                             
2020073c  track_ctrl                           
20200660  uart_rx_buffer                       
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
2020077c  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  Motor_Square_Corner_Control          
00000445  SYSCFG_DL_GPIO_init                  
000005d9  __aeabi_dsub                         
000005d9  __subdf3                             
000005e3  __adddf3                             
000005e3  __aeabi_dadd                         
0000076b  __aeabi_idiv0                        
0000076d  No_MCU_Ganv_Sensor_Init              
000008f5  Set_PWM                              
00000a61  Key_Scan_Debounce                    
00000bbd  Track_Basic_Control                  
00000d11  main                                 
00000e5d  GROUP1_IRQHandler                    
00000f7d  Motor_Smooth_Control                 
0000108d  __aeabi_ddiv                         
0000108d  __divdf3                             
00001199  DL_Timer_initFourCCPWMMode           
0000129d  TIMG0_IRQHandler                     
0000139d  DL_Timer_initTimerMode               
00001485  __aeabi_dmul                         
00001485  __muldf3                             
00001569  OLED_ShowNum                         
0000164b  OLED_Init                            
00001729  DL_SYSCTL_configSYSPLL               
00001805  __aeabi_fsub                         
00001805  __subsf3                             
0000180f  __addsf3                             
0000180f  __aeabi_fadd                         
000018dd  Get_Analog_value                     
000019ad  Analyze_Track_State                  
00001a65  Motor_PID_Control                    
00001b1d  normalizeAnalogValues                
00001bc9  Calculate_Line_Position              
00001c71  Motor_Speed_Monitor                  
00001d19  OLED_ShowSignedNum                   
00001db3  OLED_ShowString                      
00001e4d  OLED_DrawPoint                       
00001edd  SYSCFG_DL_PWM_0_init                 
00001f69  SYSCFG_DL_initPower                  
00001ff5  __aeabi_fmul                         
00001ff5  __mulsf3                             
00002081  OLED_Refresh                         
00002105  Way_Optimized                        
0000220d  __aeabi_fdiv                         
0000220d  __divsf3                             
00002291  __TI_decompress_lzss                 
0000230d  Motor_Verify_Speed_Consistency       
00002381  Track_Adaptive_Control               
000023f5  Track_Init                           
00002469  __gedf2                              
00002469  __gtdf2                              
000024dd  No_MCU_Ganv_Sensor_Init_Frist        
00002551  OLED_WR_Byte                         
000025bd  convertAnalogToDigital               
00002629  Handle_Lost_Line                     
00002691  Key_1                                
000026f9  __cmpdf2                             
000026f9  __eqdf2                              
000026f9  __ledf2                              
000026f9  __ltdf2                              
000026f9  __nedf2                              
00002761  __aeabi_dcmpeq                       
00002775  __aeabi_dcmplt                       
00002789  __aeabi_dcmple                       
0000279d  __aeabi_dcmpge                       
000027b1  __aeabi_dcmpgt                       
000027c5  __aeabi_fcmpeq                       
000027d9  __aeabi_fcmplt                       
000027ed  __aeabi_fcmple                       
00002801  __aeabi_fcmpge                       
00002815  __aeabi_fcmpgt                       
00002829  OLED_Clear                           
00002889  DL_I2C_fillControllerTXFIFO          
000028e9  SYSCFG_DL_I2C_OLED_init              
00002941  SYSCFG_DL_UART_0_init                
00002b19  __aeabi_d2iz                         
00002b19  __fixdfsi                            
00002b63  adc_getValue                         
00002bad  DL_UART_init                         
00002bf5  OLED_DisplayTurn                     
00002c3d  SYSCFG_DL_ADC12_0_init               
00002c85  SYSCFG_DL_SYSCTL_init                
00002ccd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002d11  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002d55  DL_ADC12_setClockConfig              
00002d95  Key                                  
00002dd5  Track_Square_Corner_Control          
00002e15  UART0_IRQHandler                     
00002e55  __aeabi_uidiv                        
00002e55  __aeabi_uidivmod                     
00002f0d  Get_Anolog_Value                     
00002f49  Track_PID_Control                    
00002f85  Track_Weighted_Control               
00002fc1  __aeabi_i2f                          
00002fc1  __floatsisf                          
00002ffd  __gesf2                              
00002ffd  __gtsf2                              
00003039  __TI_auto_init_nobinit_nopinit       
00003075  __cmpsf2                             
00003075  __eqsf2                              
00003075  __lesf2                              
00003075  __ltsf2                              
00003075  __nesf2                              
000030b1  __muldsi3                            
000030ed  __aeabi_f2iz                         
000030ed  __fixsfsi                            
00003159  OLED_ColorTurn                       
0000318d  SYSCFG_DL_TIMER_0_init               
000031c1  SYSCFG_DL_init                       
00003225  OLED_Pow                             
00003255  SysTick_Handler                      
000032dd  __aeabi_i2d                          
000032dd  __floatsidf                          
00003309  strncpy                              
00003335  Calculate_Position_Error             
00003425  __aeabi_ui2f                         
00003425  __floatunsisf                        
0000344d  _c_int00_noargs                      
0000349b  DL_I2C_setClockConfig                
000034c1  Left_Control                         
000034e5  Left_Little_Control                  
00003509  Right_Control                        
0000352d  Right_Little_Control                 
00003551  __aeabi_ui2d                         
00003551  __floatunsidf                        
000035b5  Motor_Reset_Speed_Monitor            
000035d5  delay_ms                             
00003749  DL_Timer_setCaptCompUpdateMethod     
00003765  DL_Timer_setClockConfig              
000039ad  DL_Timer_setCaptureCompareOutCtl     
00003a0d  Handle_Intersection                  
00003b71  Key_Init_Debounce                    
00003ba9  DL_UART_setClockConfig               
00003bbb  TI_memcpy_small                      
00003bcd  __TI_decompress_none                 
00003c01  DL_Timer_setCaptureCompareValue      
00003c11  Key_System_Tick_Inc                  
00003c21  __TI_zero_init                       
00003c31  Get_Digtal_For_User                  
00003c3f  strcpy                               
00003c4d  TI_memset_small                      
00003c5b  SYSCFG_DL_SYSTICK_init               
00003c69  __aeabi_memclr                       
00003c69  __aeabi_memclr4                      
00003c69  __aeabi_memclr8                      
00003c75  get_systicks                         
00003c81  scheduler_init                       
00003c8d  DL_Common_delayCycles                
00003c99  __aeabi_memcpy                       
00003c99  __aeabi_memcpy4                      
00003c99  __aeabi_memcpy8                      
00003ca1  abort                                
00003ca7  ADC0_IRQHandler                      
00003ca7  ADC1_IRQHandler                      
00003ca7  AES_IRQHandler                       
00003ca7  CANFD0_IRQHandler                    
00003ca7  DAC0_IRQHandler                      
00003ca7  DMA_IRQHandler                       
00003ca7  Default_Handler                      
00003ca7  GROUP0_IRQHandler                    
00003ca7  HardFault_Handler                    
00003ca7  I2C0_IRQHandler                      
00003ca7  I2C1_IRQHandler                      
00003ca7  NMI_Handler                          
00003ca7  PendSV_Handler                       
00003ca7  RTC_IRQHandler                       
00003ca7  SPI0_IRQHandler                      
00003ca7  SPI1_IRQHandler                      
00003ca7  SVC_Handler                          
00003ca7  TIMA0_IRQHandler                     
00003ca7  TIMA1_IRQHandler                     
00003ca7  TIMG12_IRQHandler                    
00003ca7  TIMG6_IRQHandler                     
00003ca7  TIMG7_IRQHandler                     
00003ca7  TIMG8_IRQHandler                     
00003ca7  UART1_IRQHandler                     
00003ca7  UART2_IRQHandler                     
00003ca7  UART3_IRQHandler                     
00003caa  C$$EXIT                              
00003cab  HOSTexit                             
00003caf  Reset_Handler                        
00003cc5  _system_pre_init                     
00003cc8  asc2_2412                            
00004a24  asc2_1608                            
00005014  asc2_1206                            
00005488  asc2_0806                            
00005794  __TI_Handler_Table_Base              
000057a0  __TI_Handler_Table_Limit             
000057a8  __TI_CINIT_Base                      
000057b8  __TI_CINIT_Limit                     
000057b8  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  motor_pid                            
2020073c  track_ctrl                           
2020075c  Anolog                               
2020076c  black                                
2020077c  white                                
2020078c  key1_ctrl                            
202007a0  D_Num                                
202007ac  Run                                  
202007b0  delay_times                          
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[241 symbols]
