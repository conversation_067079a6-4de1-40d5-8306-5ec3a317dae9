#include "ti_msp_dl_config.h"
#include "systick.h"

volatile unsigned int delay_times = 0;
//搭配滴答定时器实现的精确ms延时
void delay_ms(unsigned int ms)
{
    delay_times = ms;
    while( delay_times != 0 );
}

static uint64_t systicks = 0;

void SysTick_Handler(void)
{
    systicks++;
    if( delay_times != 0 )
    {
        delay_times--;
    }
}

uint64_t get_systicks(void)
{
    return systicks;
}

void led_proc(void)
{   
    static int i = 0;
    if(i == 0)
    {
        DL_GPIO_clearPins(LED_PORT,LED_PIN_22_PIN);//输出低电平
        i = 1;
    }
    else 
    {
        DL_GPIO_setPins(LED_PORT,LED_PIN_22_PIN);  //输出高电平 
        i = 0;
    }


}