## Example Summary

智能循迹小车控制系统 - 正方形轨道专用版本
本项目基于MSPM0G3507微控制器，实现了针对正方形轨道优化的循迹控制系统，
特别针对直角转弯进行了精确控制优化。

### 主要特性
- **精确直角转弯控制**：采用"一轮停止，一轮转动"策略，解决高速时拐角过不去的问题
- **多模式循迹算法**：支持基础、加权、PID、自适应四种控制模式
- **速度自适应调整**：根据轨道状态动态调整速度，提高循迹精度
- **完整的调试支持**：提供丰富的调试信息和参数调优功能

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  |  |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## 循迹系统使用说明

### 快速开始
1. 编译并下载程序到MSPM0G3507开发板
2. 连接循迹传感器和电机驱动模块
3. 调用相应的示例函数开始循迹

### 正方形轨道专用功能

#### 精确直角转弯控制
针对正方形轨道的直角特性，实现了专门的转弯控制策略：
- 检测到直角转弯时，一个轮子停止（速度=0），另一个轮子转动
- 确保最小转弯半径，避免因速度过快导致的拐角过不去问题

#### 使用示例
```c
// 基础使用
Square_Track_Basic_Example(sensor);

// 精确直角转弯演示
Square_Track_Precise_Corner_Example(sensor);

// 速度自适应控制
Square_Track_Speed_Adaptive_Example(sensor);
```

### 配置参数
- `TRACK_SQUARE_STRAIGHT_SPEED`: 直线速度 (3200)
- `TRACK_SQUARE_CORNER_SPEED`: 转弯速度 (2400)
- `TRACK_SQUARE_CORNER_RATIO`: 直角转弯速度比例 (0.65)

### 文件结构
- `app/Ganway_Optimized.c/h`: 优化的循迹算法核心
- `app/Square_Track_Example.c/h`: 正方形轨道专用示例
- `app/Square_Track_Test.c/h`: 测试和调试程序
- `app/Track_Config.h`: 配置参数定义
- `正方形轨道精确转弯控制说明.md`: 详细技术说明

### 调试和优化
使用 `Square_Track_Debug_Example()` 可以实时监控循迹状态，
通过调整配置参数来优化不同轨道的循迹效果。

详细的技术原理和使用方法请参考 `正方形轨道精确转弯控制说明.md`。
