# 正方形轨道精确直角转弯控制说明

## 问题描述

在正方形轨道循迹中，由于轨道的直角特性，传统的差速控制方法在高速情况下容易出现以下问题：
- 速度过快时，机器人在直角拐弯处因惯性无法精确转弯
- 两轮都有速度时，转弯半径较大，容易偏离轨道
- 直角转弯需要更精确的控制策略

## 解决方案：一轮停止，一轮转动

### 核心思想
在检测到直角转弯时，采用"一轮停止（速度为0），另一轮转动"的策略：
- **右转时**：左轮转动，右轮停止（速度=0）
- **左转时**：右轮转动，左轮停止（速度=0）

这样可以实现最小转弯半径，确保机器人能够精确通过直角。

### 实现原理

#### 1. 直角检测机制
```c
// 连续检测大偏差，判定为直角转弯
if(abs(error) > TRACK_SQUARE_SHARP_TURN_THRESHOLD) {
    sharp_turn_counter++;
    
    if(sharp_turn_counter > TRACK_SQUARE_SHARP_TURN_COUNT) {
        // 确认为直角转弯，使用精确控制
    }
}
```

#### 2. 精确转弯控制
```c
if(turn_adjustment > 0) {
    // 右转：左轮转动，右轮停止
    left_speed = turn_speed;
    right_speed = 0;
} else if(turn_adjustment < 0) {
    // 左转：右轮转动，左轮停止
    left_speed = 0;
    right_speed = turn_speed;
}
```

#### 3. 渐进式控制
- 刚检测到偏差时，使用传统差速控制
- 连续检测到大偏差时，切换到精确控制
- 偏差减小后，平滑过渡回正常控制

## 配置参数

### 关键参数说明
```c
#define TRACK_SQUARE_CORNER_RATIO   0.65f   // 直角转弯时速度比例
#define TRACK_SQUARE_SHARP_TURN_THRESHOLD  3    // 直角转弯检测阈值
#define TRACK_SQUARE_SHARP_TURN_COUNT      2    // 连续检测次数阈值
```

### 速度配置
```c
#define TRACK_SQUARE_STRAIGHT_SPEED 3200    // 直线速度
#define TRACK_SQUARE_CORNER_SPEED   2400    // 转弯速度
```

## 使用方法

### 1. 基础使用
```c
// 初始化
Track_Init();
Track_Set_Mode(TRACK_MODE_ADAPTIVE);  // 使用自适应模式
Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);

// 主循环
while(1) {
    // 获取传感器数据
    No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
    digital_data = Get_Digtal_For_User(sensor);
    Get_Anolog_Value(sensor, analog_data);
    
    // 执行优化的循迹算法（自动包含精确转弯控制）
    Way_Optimized(digital_data, analog_data);
    
    delay_ms(10);
}
```

### 2. 专用示例函数
```c
// 精确直角转弯示例
Square_Track_Precise_Corner_Example(sensor);

// 速度自适应示例
Square_Track_Speed_Adaptive_Example(sensor);
```

## 控制流程

### 状态转换
1. **正常状态** → 检测到大偏差 → **渐进控制**
2. **渐进控制** → 连续大偏差 → **精确转弯**
3. **精确转弯** → 偏差减小 → **平滑过渡** → **正常状态**

### 速度调整策略
- **直线段**：使用 `TRACK_SQUARE_STRAIGHT_SPEED`
- **检测到转弯**：降速到 `TRACK_SQUARE_CORNER_SPEED`
- **直角转弯**：进一步降速（乘以 `TRACK_SQUARE_CORNER_RATIO`）

## 优势特点

1. **精确性**：一轮停止确保最小转弯半径
2. **稳定性**：渐进式控制避免突然变化
3. **适应性**：自动检测直角并切换控制策略
4. **平滑性**：转弯后平滑过渡回正常控制

## 调试建议

### 参数调优
1. 如果转弯过慢：增大 `TRACK_SQUARE_CORNER_RATIO`
2. 如果检测不敏感：减小 `TRACK_SQUARE_SHARP_TURN_THRESHOLD`
3. 如果误检测：增大 `TRACK_SQUARE_SHARP_TURN_COUNT`

### 调试输出
使用 `Square_Track_Debug_Example()` 函数可以实时监控：
- 当前状态
- 转弯计数
- 速度变化
- 误差值

## 注意事项

1. **传感器精度**：确保传感器能准确检测直角
2. **速度匹配**：根据实际轨道调整速度参数
3. **电机响应**：确保电机能快速响应速度变化
4. **轨道质量**：轨道的直角度会影响检测效果

## 扩展应用

这种控制策略不仅适用于正方形轨道，也可以用于：
- 矩形轨道
- 其他包含直角的轨道
- 需要精确转弯的场景
