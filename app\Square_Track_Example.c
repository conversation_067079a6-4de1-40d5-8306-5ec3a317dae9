/**
 * @file Square_Track_Example.c
 * @brief 正方形轨道循迹专用示例
 * @details 针对正方形轨道的直角转弯优化配置和使用方法
 */

#include "Ganway_Optimized.h"
#include "Track_Config.h"
#include "bsp_system.h"

/**
 * @brief 正方形轨道循迹示例 - 基础配置
 * @param sensor 传感器对象指针
 */
void Square_Track_Basic_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    
    // 1. 初始化循迹系统
    Track_Init();
    
    // 2. 设置为自适应模式（推荐用于正方形轨道）
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    
    // 3. 设置适合正方形轨道的速度
    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);  // 3200，比原来的4000慢
    
    // 4. 主循环
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        // 执行优化的循迹算法
        Way_Optimized(digital_data, analog_data);
        
        // 可选：输出调试信息
        #if TRACK_DEBUG_ENABLE
        Track_Debug_Info();
        #endif
        
        delay_ms(10);  // 控制循环频率
    }
}

/**
 * @brief 正方形轨道循迹示例 - 高级配置（动态速度调整）
 * @param sensor 传感器对象指针
 */
void Square_Track_Advanced_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    static int corner_detect_count = 0;
    
    // 初始化
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
    
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        // 获取循迹状态
        status = Track_Get_Status();
        
        // 根据状态动态调整速度
        switch(status->state) {
            case TRACK_STATE_NORMAL:
                // 直线状态，使用正常速度
                Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
                corner_detect_count = 0;
                break;
                
            case TRACK_STATE_TURN_LEFT:
            case TRACK_STATE_TURN_RIGHT:
                // 检测到转弯，计数器增加
                corner_detect_count++;
                
                if(corner_detect_count > 3) {  // 连续检测到转弯，可能是直角
                    // 使用专用的直角转弯速度
                    Track_Set_Speed(TRACK_SQUARE_CORNER_SPEED);
                } else {
                    // 可能是轻微偏移，使用中等速度
                    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED * 0.9f);
                }
                break;
                
            case TRACK_STATE_LOST:
                // 丢线状态，大幅降速
                Track_Set_Speed(TRACK_SQUARE_CORNER_SPEED * 0.8f);
                break;
                
            default:
                Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
                break;
        }
        
        // 执行循迹
        Way_Optimized(digital_data, analog_data);
        
        delay_ms(10);
    }
}

/**
 * @brief 正方形轨道循迹示例 - 调试模式
 * @param sensor 传感器对象指针
 */
void Square_Track_Debug_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    static int debug_counter = 0;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
    
    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        Way_Optimized(digital_data, analog_data);
        
        // 每100次循环输出一次调试信息
        debug_counter++;
        if(debug_counter >= 100) {
            debug_counter = 0;
            status = Track_Get_Status();
            
            // 可以通过OLED或串口输出状态信息
            // OLED_ShowString(0, 0, "Square Track", 12, 1);
            // OLED_ShowNum(0, 12, status->base_speed, 4, 12, 1);
            // OLED_ShowNum(0, 24, status->error, 4, 12, 1);
            
            #if TRACK_DEBUG_ENABLE
            Track_Debug_Info();
            #endif
        }
        
        delay_ms(10);
    }
}

/**
 * @brief 正方形轨道参数测试示例
 * @param sensor 传感器对象指针
 * @note 用于测试不同参数组合的效果
 */
void Square_Track_Parameter_Test(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    static int test_phase = 0;
    static int test_counter = 0;

    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);

    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);

        // 每5秒切换一次测试参数
        test_counter++;
        if(test_counter >= 500) {  // 5秒 = 500 * 10ms
            test_counter = 0;
            test_phase = (test_phase + 1) % 4;

            switch(test_phase) {
                case 0:
                    // 测试慢速模式
                    Track_Set_Speed(TRACK_BASE_SPEED_SLOW);
                    // OLED_ShowString(0, 0, "Test: Slow", 12, 1);
                    break;
                case 1:
                    // 测试正常模式
                    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
                    // OLED_ShowString(0, 0, "Test: Normal", 12, 1);
                    break;
                case 2:
                    // 测试转弯专用速度
                    Track_Set_Speed(TRACK_SQUARE_CORNER_SPEED);
                    // OLED_ShowString(0, 0, "Test: Corner", 12, 1);
                    break;
                case 3:
                    // 测试快速模式
                    Track_Set_Speed(TRACK_BASE_SPEED_FAST);
                    // OLED_ShowString(0, 0, "Test: Fast", 12, 1);
                    break;
            }
        }

        Way_Optimized(digital_data, analog_data);
        delay_ms(10);
    }
}

/**
 * @brief 正方形轨道精确直角转弯示例
 * @param sensor 传感器对象指针
 * @note 专门演示"一轮停止，一轮转动"的直角转弯策略
 */
void Square_Track_Precise_Corner_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    static int corner_count = 0;
    static int debug_counter = 0;

    // 初始化
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);  // 使用自适应模式，会自动调用精确转弯控制
    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);  // 设置适中的直线速度

    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);

        // 获取循迹状态
        status = Track_Get_Status();

        // 监控转弯状态
        if(status->state == TRACK_STATE_TURN_LEFT || status->state == TRACK_STATE_TURN_RIGHT) {
            corner_count++;
        }

        // 执行优化的循迹算法（包含精确直角转弯控制）
        Way_Optimized(digital_data, analog_data);

        // 调试信息输出（每100次循环输出一次）
        debug_counter++;
        if(debug_counter >= 100) {
            debug_counter = 0;

            #if TRACK_DEBUG_ENABLE
            // 可以通过OLED显示当前状态
            // OLED_ShowString(0, 0, "Precise Corner", 12, 1);
            // OLED_ShowString(0, 12, "State:", 12, 1);
            // OLED_ShowNum(36, 12, status->state, 1, 12, 1);
            // OLED_ShowString(0, 24, "Corners:", 12, 1);
            // OLED_ShowNum(48, 24, corner_count, 3, 12, 1);
            // OLED_ShowString(0, 36, "Speed:", 12, 1);
            // OLED_ShowNum(36, 36, status->base_speed, 4, 12, 1);
            // OLED_Refresh();

            Track_Debug_Info();
            #endif
        }

        delay_ms(10);  // 控制循环频率
    }
}

/**
 * @brief 正方形轨道速度自适应示例
 * @param sensor 传感器对象指针
 * @note 根据轨道状态动态调整速度，直角转弯时自动降速并使用精确控制
 */
void Square_Track_Speed_Adaptive_Example(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    static int straight_counter = 0;
    static int corner_counter = 0;

    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);

    while(1) {
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);

        status = Track_Get_Status();

        // 根据状态动态调整策略
        switch(status->state) {
            case TRACK_STATE_NORMAL:
                // 直线状态，逐渐提高速度
                straight_counter++;
                corner_counter = 0;

                if(straight_counter > 50) {  // 连续直线50次循环后提速
                    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
                }
                break;

            case TRACK_STATE_TURN_LEFT:
            case TRACK_STATE_TURN_RIGHT:
                // 转弯状态，立即降速并使用精确控制
                corner_counter++;
                straight_counter = 0;

                if(corner_counter > 3) {  // 连续转弯，可能是直角
                    Track_Set_Speed(TRACK_SQUARE_CORNER_SPEED);  // 使用专用转弯速度
                }
                break;

            case TRACK_STATE_LOST:
                // 丢线状态，大幅降速
                Track_Set_Speed(TRACK_SQUARE_CORNER_SPEED * 0.8f);
                straight_counter = 0;
                corner_counter = 0;
                break;

            default:
                straight_counter = 0;
                corner_counter = 0;
                break;
        }

        Way_Optimized(digital_data, analog_data);
        delay_ms(10);
    }
}
